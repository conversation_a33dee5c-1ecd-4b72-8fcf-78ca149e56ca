import React, { useContext } from 'react'

import CheckboxGroup from 'antd/lib/checkbox/Group'
import { Category } from 'src/v2/models/Filter'
import useFilterOptions from '../../hooks/useFilterOptions'
import { CollapseStyled, SubCategoryPanelStyled } from './SubCategoryPanelStyled'
import FilterItem from '../FilterItem/FilterItem'
import HeaderSubCategory from './HeaderSubCategory'
import PlusIconThin from 'src/v2/assets/icons/PlusIconThin'
import MinusIconThin from 'src/v2/assets/icons/MinusIconThin'

import FiltersContext from 'src/v2/contexts/FiltersContext'

interface SubCategoryPanelProps {
  category: Category
  appliedSubCategoriesByCategories?: any
  handleChangeSelectedItem?: (value: number) => void
  filterName: string
}

const SubCategoryPanel: React.FC<SubCategoryPanelProps> = ({
  category,
  appliedSubCategoriesByCategories,
  handleChangeSelectedItem,
  filterName
}) => {
  const { filtersApplied, appliedFilterToShow, setAppliedFilterToShow } = useContext(FiltersContext)
  const { getLanguage } = useFilterOptions()
  const selectedLanguage = getLanguage(category.languages)
  const badge = appliedSubCategoriesByCategories?.[category.name] ?? null
  const appliedFilter = filtersApplied[filterName]
  const selectedFilters = appliedFilterToShow[filterName]

  const allChecked = () => {
    if (category.subCategories && selectedFilters && selectedFilters.value) {
      const selectedsubCategories = category.subCategories.filter((subCategory) =>
        selectedFilters.value.includes(subCategory.id)
      )
      return category.subCategories.length === selectedsubCategories.length
    }

    return false
  }

  const isIndeterminate = () => {
    if (category.subCategories && selectedFilters && selectedFilters.value) {
      const selectedsubCategories = category.subCategories.filter((subCategory) =>
        selectedFilters.value.includes(subCategory.id)
      )
      return (
        selectedsubCategories.length < category.subCategories.length &&
        selectedsubCategories.length > 0
      )
    }

    return false
  }

  const handleChangeCheckedAll = () => {
    const values = selectedFilters ? selectedFilters.value : []
    const items = category.subCategories.map((subCategory) => subCategory.id)

    if (allChecked()) {
      setAppliedFilterToShow({
        ...appliedFilterToShow,
        [filterName]: {
          value: values.filter((value: number) => !items.includes(value))
        }
      })
    } else {
      setAppliedFilterToShow({
        ...appliedFilterToShow,
        [filterName]: {
          value: [...values, ...items]
        }
      })
    }
  }

  return (
    <CollapseStyled
      expandIconPosition="right"
      accordion
      expandIcon={(status) => <span>{status.isActive ? <MinusIconThin /> : <PlusIconThin />}</span>}
    >
      <SubCategoryPanelStyled
        header={
          <HeaderSubCategory
            name={selectedLanguage.name}
            badge={badge}
            isChecked={allChecked()}
            isIndeterminate={isIndeterminate()}
            handleChange={handleChangeCheckedAll}
          />
        }
        key="1"
      >
        <CheckboxGroup
          style={{ width: '100%', background: 'var(--blue-energy-t2-opacity3)' }}
          value={selectedFilters ? selectedFilters.value : []}
        >
          {category.subCategories.map((subCategory) => {
            const selectedLanguageSubCategory = getLanguage(subCategory.languages)
            return (
              <FilterItem
                backgroundColor="var(--blue-energy-t2-opacity3)"
                id={subCategory.id}
                name={selectedLanguageSubCategory.name}
                key={`${selectedLanguageSubCategory.name}-${subCategory.id}`}
                handleChangeSelectedItem={handleChangeSelectedItem}
                appliedFilter={appliedFilter ? appliedFilter.value : []}
              />
            )
          })}
        </CheckboxGroup>
      </SubCategoryPanelStyled>
    </CollapseStyled>
  )
}
export default SubCategoryPanel

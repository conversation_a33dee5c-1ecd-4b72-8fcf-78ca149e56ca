import React, { useContext } from 'react'
import PropTypes from 'prop-types'
import { useHistory, useLocation } from 'react-router-dom'

import AdobeAnalytics from 'src/v2/intances/AdobeAnalytics'

import FiltersContext from 'src/v2/contexts/FiltersContext'

import { ApplyFilterButtonStyled } from './ApplyFilterButtonStyled'

const { eventDispatch } = AdobeAnalytics

interface ApplyFilterButtonProps {
  handleClick?: () => void
}

const ApplyFilterButton: React.FC<ApplyFilterButtonProps> = ({ handleClick }, context) => {
  const { appliedFilterToShow, filtersApplied, changeLanguage, campaignId } =
    useContext(FiltersContext)
  const history = useHistory()
  const location = useLocation()

  const handleClickApplyFilters = () => {
    const currentUrlParams = new URLSearchParams(location.search)

    if (changeLanguage && campaignId) {
      const languageValues = appliedFilterToShow.language.value
      if (languageValues && languageValues.length > 0) {
        const selectedLanguageId = languageValues[languageValues.length - 1]
        changeLanguage(selectedLanguageId)
        return
      }
    }

    if (Object.keys(appliedFilterToShow).length > 0) {
      const filterParams = JSON.stringify(appliedFilterToShow)
      currentUrlParams.set('filters', filterParams)
    } else {
      currentUrlParams.delete('filters')
    }

    currentUrlParams.get('assetPage') && currentUrlParams.delete('assetPage')
    currentUrlParams.get('campaignPage') && currentUrlParams.delete('campaignPage')
    currentUrlParams.get('resourcePage') && currentUrlParams.delete('resourcePage')

    history.push({
      pathname: location.pathname,
      search: decodeURIComponent(currentUrlParams.toString())
    })
    eventDispatch({
      action: 'click',
      category: 'fiters_bar',
      label: 'apply_filters_button'
    })
    if (handleClick) handleClick()
  }

  const isDisabledButtonApply = () => {
    const filtersAppliedToArray = Object.entries(appliedFilterToShow)
    if (filtersAppliedToArray.length !== Object.keys(filtersApplied).length) {
      return false
    }

    let disabled = true
    for (let i = 0; i <= filtersAppliedToArray.length; i++) {
      const appliedFilter = filtersAppliedToArray[i]
      if (appliedFilter) {
        const [filterName, entries] = appliedFilter as any
        const selectedFilter = filtersApplied[filterName]
        if (selectedFilter && entries.value.length === selectedFilter.value.length) {
          disabled = entries.value.every((entry: number) => selectedFilter.value.includes(entry))
        } else {
          disabled = false
          break
        }
      }
    }

    return disabled
  }

  return (
    <ApplyFilterButtonStyled onClick={handleClickApplyFilters} disabled={isDisabledButtonApply()}>
      {context.t('customization').applyButton}
    </ApplyFilterButtonStyled>
  )
}

ApplyFilterButton.contextTypes = {
  t: PropTypes.func.isRequired
}

export default ApplyFilterButton

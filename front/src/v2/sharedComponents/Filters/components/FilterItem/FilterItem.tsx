import React, { useState, useEffect, useContext } from 'react'

import { Checkbox } from 'antd'

import FilterItemStyled from './FilterItemStyled'
import { CheckboxChangeEvent } from 'antd/lib/checkbox'
import FiltersContext from 'src/v2/contexts/FiltersContext'

interface FilterItemProps {
  id: number
  name: string
  backgroundColor?: string
  handleChangeSelectedItem?: (value: number) => void
  appliedFilter: number[]
}

const FilterItem = ({
  id,
  name = '',
  backgroundColor,
  handleChangeSelectedItem,
  appliedFilter
}: FilterItemProps) => {
  const { changeInAppliedFiltersFlag, resetFilter } = useContext(FiltersContext)
  const [isChecked, setIsChecked] = useState<boolean>(
    appliedFilter ? appliedFilter.includes(id) : false
  )

  useEffect(() => {
    setIsChecked(appliedFilter ? appliedFilter.includes(id) : false)
  }, [changeInAppliedFiltersFlag, resetFilter])

  const onChangeCheckbox = (e: CheckboxChangeEvent) => {
    const { value } = e.target
    if (handleChangeSelectedItem) {
      handleChangeSelectedItem(value)
    }
    setIsChecked((prevIsChecked) => !prevIsChecked)
  }

  return (
    <FilterItemStyled backgroundColor={backgroundColor}>
      <Checkbox
        value={id}
        id={`${name}-${id.toString()}`}
        onChange={onChangeCheckbox}
        checked={isChecked}
      />
      <label htmlFor={`${name}-${id.toString()}`} className="label-text">
        {name} hola
      </label>
    </FilterItemStyled>
  )
}

export default FilterItem

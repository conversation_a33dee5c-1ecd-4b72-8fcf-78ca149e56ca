import React, { useCallback, useContext } from 'react'

import { Category } from 'src/v2/models/Filter'

import useFilterOptions from '../../hooks/useFilterOptions'
import FilterItem from '../FilterItem/FilterItem'
import SubCategoryPanel from '../SubCategoryPanel/SubCategoryPanel'
import FiltersContext from 'src/v2/contexts/FiltersContext'

export interface CategoryPanelProps {
  categories: Category[]
  appliedSubCategoriesByCategories?: any
  filterName: string
  className?: string // Used for styled components
}

const CategoryPanel = ({
  categories,
  appliedSubCategoriesByCategories,
  filterName,
  className
}: CategoryPanelProps) => {
  const { getLanguage } = useFilterOptions()
  const { setAppliedFilterToShow, appliedFilterToShow, filtersApplied } = useContext(FiltersContext)

  const addOrRemoveItem = (values: number[], value: number) => {
    const thereIsValue = values.includes(value)
    if (thereIsValue) {
      return values.filter((currentValue) => currentValue !== value)
    }

    return [...values, value]
  }

  const handleChangeSelectedItem = useCallback(
    (item: number) => {
      console.log(`CategoryPanel - ${filterName} filter changed, item:`, item)
      const filterValues = appliedFilterToShow[filterName]
        ? appliedFilterToShow[filterName].value
        : []
      console.log(`CategoryPanel - current ${filterName} values:`, filterValues)
      const values = addOrRemoveItem(filterValues, item)
      console.log(`CategoryPanel - new ${filterName} values:`, values)

      if (values.length) {
        setAppliedFilterToShow((prev: any) => {
          return {
            ...prev,
            [filterName]: {
              value: values
            }
          }
        })
      } else {
        setAppliedFilterToShow((prev: any) => {
          const updatedFilterToShow = { ...prev }
          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
          delete updatedFilterToShow[filterName]
          return updatedFilterToShow
        })
      }
    },
    [setAppliedFilterToShow, appliedFilterToShow, filterName]
  )

  return (
    <div className={className}>
      {categories.map((category: Category) => {
        const hasSubCategories = !!category.subCategories
        if (hasSubCategories) {
          return (
            <SubCategoryPanel
              category={category}
              key={category.id}
              appliedSubCategoriesByCategories={appliedSubCategoriesByCategories}
              handleChangeSelectedItem={handleChangeSelectedItem}
              filterName={filterName}
            />
          )
        }

        const selectedLanguage = getLanguage(category.languages)
        const appliedFilter = filtersApplied[filterName]
        const name = selectedLanguage.name === '' ? category.name : selectedLanguage.name
        return (
          <FilterItem
            name={name}
            id={category.id}
            key={category.id}
            handleChangeSelectedItem={handleChangeSelectedItem}
            appliedFilter={appliedFilter ? appliedFilter.value : []}
          />
        )
      })}
    </div>
  )
}
export default CategoryPanel

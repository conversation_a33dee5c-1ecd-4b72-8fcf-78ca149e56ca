import React, { useState, useEffect } from 'react'
// import useFilters from '../hooks/useFilters'
import useNewFilters from '../hooks/useNewFilters'
import { Filter } from '../models/Filter'

interface FiltersContextState {
  filters: Filter[]
  filtersApplied: any
  changeInAppliedFiltersFlag: boolean
  appliedFilterToShow: any
  setAppliedFilterToShow: any
  hasOpenPanel: boolean
  setHasOpenPanel: any
  loading: boolean
  hanldeResetFilter: () => void
  resetFilter: boolean
  changeLanguage?: (languageId: number) => void
  campaignId?: number
}

const FiltersContext = React.createContext<FiltersContextState>({
  filters: [],
  filtersApplied: {},
  changeInAppliedFiltersFlag: false,
  appliedFilterToShow: {},
  setAppliedFilterToShow: () => {},
  hasOpenPanel: false,
  setHasOpenPanel: () => {},
  loading: true,
  hanldeResetFilter: () => {},
  resetFilter: false,
  changeLanguage: undefined,
  campaignId: undefined
})

interface FiltersContextProviderProps {
  children: React.ReactNode | React.ReactNode[]
  changeLanguage?: (languageId: number) => void
  campaignId?: number
}

export const FiltersContextProvider = ({
  children,
  changeLanguage,
  campaignId
}: FiltersContextProviderProps) => {
  // const { filters, filtersApplied, changeInAppliedFiltersFlag, loading } = useFilters() // // Usar para funcionamiento independiente de barra de filtros
  const { filters, filtersApplied, changeInAppliedFiltersFlag, loading, mustUpdateFiltersHandler } =
    useNewFilters()
  const [appliedFilterToShow, setAppliedFilterToShow] = useState<any>(filtersApplied)
  const [hasOpenPanel, setHasOpenPanel] = useState(false)
  const [resetFilter, setResetFilter] = useState<boolean>(false)

  const hanldeResetFilter = () => {
    setResetFilter(!resetFilter)
  }

  useEffect(() => {
    if (Object.values(filtersApplied).length > 0) {
      mustUpdateFiltersHandler()
    }
  }, [resetFilter])

  useEffect(() => {
    setAppliedFilterToShow(filtersApplied)
  }, [changeInAppliedFiltersFlag, resetFilter])

  return (
    <FiltersContext.Provider
      value={{
        filters,
        filtersApplied,
        changeInAppliedFiltersFlag,
        appliedFilterToShow,
        setAppliedFilterToShow,
        hasOpenPanel,
        setHasOpenPanel,
        loading,
        hanldeResetFilter,
        resetFilter,
        changeLanguage,
        campaignId
      }}
    >
      {children}
    </FiltersContext.Provider>
  )
}
export default FiltersContext

.campaignPage {

    .h2-presentation{
        margin-bottom: 10px;
    }

  .header-campaign-detail {
    padding-top: 33px;
    padding-bottom: 65px;

    .title-p-h2{
        margin-bottom: 10px;
    }

    .go-back-link {
      position: absolute;
      left: -40px;
      margin: 0;
      margin-top: 30px;
    }

    .titles-top-space {
      margin-top: 20px;
    }

    .embargo-legend {
      padding-top: 5px;
      font-weight: 500;
      margin-bottom: 10px;
    }

    .overview-text {
      line-height: 1.25;
      // margin-top: 10px;
      p {
        sup {
          font-size: 13px !important;
          line-height: 10px;
        }
        sub {
          font-size: 10px !important;
          line-height: 10px;
        }
        margin-top: 10px;
        margin-bottom: 10px;
        span, strong, em {
          font-family: $font-intel-clear;
        }
      }
    }

    .main-benefits {
      margin-top: 24px;
      // h2 {
      // }

      .main-benefits-text {
        margin-top: 10px;
        line-height: 1.25;

        ul {
          padding: 0;
        }

        li {
          margin-bottom: 10px;
          position: relative;
          margin-left: 20px;
          list-style: none;
          //display: flex;
          display: block;
          //align-items: flex-start;
          //flex-direction: column;

          &::before {
            background: url('/images/rs-icon-list-bullet.svg');
            background-size: cover;
            position: absolute;
            content: '';
            left: -20px;
            height: 8px;
            width: 8px;
            top: 6px;
          }

          // &:first-of-type::before {
          //   top: 11px;
          // }
        }
      }
    }

    .rs-modal-disclaimers h4 {
      color: $greyish-brown2;
      font-weight: normal;
    }

    .disclaimer-text {
      text-decoration: underline;
      width: -moz-fit-content;
      width: fit-content;
      margin-top: 30px;
    }
    .modal-disclaimers-text {
      height: 430px;
      overflow-y: auto;
      margin: 15px;
      padding: 15px 45px 15px 15px;
      overflow-wrap: break-word;
      scrollbar-width: thin;

      &::-webkit-scrollbar {
        width: 6px;
        background: $very-light-pink-two;
        -webkit-border-radius: 5ex;
        -webkit-box-shadow: 0px 0px 1px 2px $very-light-pink-two;
      }
      &::-webkit-scrollbar-thumb {
        background: $brown-grey;
        -webkit-border-radius: 2ex;
      }
    }

    .campaign-guide {
      align-items: center;
      margin-top: 40px;
      display: flex;

      .guide-text {
        margin-left: 11.5px;
        a {
          cursor: pointer;
        }
        p {
          margin: 0;
        }
      }
    }

    h2 {
      font-weight: normal;
    }

    .language-selector .rs-select {
      margin-top: 30px;
      margin-right: 20px;

      select {
        text-align-last: left;
        font-size: inherit;
      }
    }

    .share-container {
      .rs-tooltip button {
        margin-right: 15px;
      }
    }

    .choose-language-text {
      line-height: 1.17;
      margin-top: 4px;
    }

    .campaign-bookmark {
      float: right;
      margin-bottom: 5px;
    }

    .campaign-preview-image {
      width: 100%;
      // margin-top: 50px;
    }
  }

  .body-campaign-detail {
    padding-top: 65px;
    padding-bottom: 80px;

    .disable-card-click .header-list .multiple-activated {
      height: 0;
      margin-bottom: 0;
      display: flex;
      align-items: center;

      button {
        position: relative;
        top: -40px;
      }
    }

    .results-all-items {
      padding-top: 0;
    }

    .bulk-actions {
      .button-multiple {
        left: unset;
        right: 0;
        top: -65px;
      }
    }
  }

  .related-section {
    padding-bottom: 110px;
    .section-separator {
      border-top: 1px solid $brown-grey;
    }
    h1 {
      margin-bottom: 0;
    }
  }

  .modal-disclaimers {
    h2 {
      margin-top: 15px;
    }

    .modal-close {
      right: 35px !important;
    }

    .msg-wrapper {
      max-height: 265px;
      overflow-y: scroll;
      padding: 0 25px 0 0;

      font-family: 'Intel Clear Wlat', Arial, sans-serif !important;
      color: $greyish-brown;

      ol,
      ul {
        padding-inline-start: 20px;
        font-size: 14px;
      }

      & * {
        font-family: 'Intel Clear Wlat', Arial, sans-serif !important;
        color: $greyish-brown !important;
      }
    }

    li {
      margin-bottom: 10px;
    }

    sup,
    sub {
      vertical-align: baseline;
      position: relative;
      top: -0.4em;
      font-size: 10px !important;
    }
    sub {
      top: 0.4em;
    }
  }
}

.search-in-component {


  box-sizing: border-box;
  margin: 0 -15px;

  &-title {
    gap: 9px;
    display: flex;
  flex-direction: column;
  }
}
